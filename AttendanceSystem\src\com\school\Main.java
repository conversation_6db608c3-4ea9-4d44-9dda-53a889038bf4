package com.school;

public class Main {
    public static void main(String[] args) {
        System.out.println("Welcome to the School Attendance Management System");
        System.out.println("Session 2: Core Domain Modelling");
        System.out.println("===========================================");
        
        // Demonstrating arrays of Student objects
        System.out.println("\n--- Student Management ---");
        Student[] students = new Student[3];
        
        // Creating student objects using different approaches
        students[0] = new Student("S001", "<PERSON> Johnson");
        students[1] = new Student();
        students[1].setDetails("S002", "<PERSON> Smith");
        students[2] = new Student();
        students[2].setDetails("S003", "Charlie Brown");
        
        // Display all students
        System.out.println("All Students:");
        for (int i = 0; i < students.length; i++) {
            System.out.println("Student " + (i + 1) + ":");
            students[i].displayDetails();
        }
        
        // Demonstrating arrays of Course objects
        System.out.println("\n--- Course Management ---");
        Course[] courses = new Course[3];
        
        // Creating course objects using different approaches
        courses[0] = new Course("CS101", "Introduction to Computer Science");
        courses[1] = new Course();
        courses[1].setDetails("MATH201", "Calculus II");
        courses[2] = new Course();
        courses[2].setDetails("ENG101", "English Literature");
        
        // Display all courses
        System.out.println("All Courses:");
        for (int i = 0; i < courses.length; i++) {
            System.out.println("Course " + (i + 1) + ":");
            courses[i].displayDetails();
        }
        
        // Demonstrating the 'this' keyword usage
        System.out.println("\n--- Demonstrating 'this' keyword ---");
        Student demoStudent = new Student();
        demoStudent.setDetails("S999", "Demo Student");
        System.out.println("Demo student created using 'this' keyword in setDetails method:");
        demoStudent.displayDetails();
        
        Course demoCourse = new Course();
        demoCourse.setDetails("DEMO101", "Demo Course");
        System.out.println("Demo course created using 'this' keyword in setDetails method:");
        demoCourse.displayDetails();
        
        System.out.println("Session 2: Core Domain Modelling Completed Successfully!");
    }
}
